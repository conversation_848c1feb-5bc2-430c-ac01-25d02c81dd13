import request from '@/utils/requestHttp.js'

/**
 * 获取工厂建模组织层级
*/

async function getAllFactory(companyCode = 'KTGS') {
    try {
        const result = await request(`/api/mom-data/momFactory/getFactoryModelAndYuntu`, {
            method: "POSt",
            body:{
                companyCode:companyCode
            }
        })

        // 检查API返回的数据是否有效
        if (!result) {
            return getDefaultFactoryData();
        }

        // 直接返回API结果
        return result;
    } catch (error) {
        console.error("获取组织层级数据失败:", error);
        // 如果发生错误，返回默认数据
        return getDefaultFactoryData();
    }
}

// 获取默认工厂数据
function getDefaultFactoryData() {
    return {
        ts: Date.now(),
        data: {
            factory: [
                {
                    parentCode: "KTGS",
                    parentName: "空调公司",
                    factoryModelCode: "6210",
                    factoryModelName: "空调公司平度工厂"
                }
            ],
            workshop: [
                {
                    parentCode: "H011080",
                    parentName: "家空分厂",
                    factoryModelCode: "6210-ZSCJ",
                    factoryModelName: "中试车间"
                }
            ],
            line: [
                {
                    parentCode: "6210-ZSCJ",
                    parentName: "中试车间",
                    factoryModelCode: "6210-ZZ-PZ",
                    factoryModelName: "中试线"
                }
            ],
            subfactory: [
                {
                    parentCode: "6210",
                    parentName: "空调公司平度工厂",
                    factoryModelCode: "H011080",
                    factoryModelName: "家空分厂"
                }
            ],
            team: [
                {
                    parentCode: "6210-ZZ-PZ",
                    parentName: "中试线",
                    factoryModelCode: "H8857",
                    factoryModelName: "中试班"
                }
            ]
        },
        code: "0",
        msg: "操作成功",
        alert: "0"
    };
}


/**
 * 获取所有字典值
*/

async function getAllDictsData() {
    let dictNames = [
        'mom-ECenterEventType',
        'mom-ECenterNodeStatus',
        'mom-ECenterCreateType',
        'mom-ECenterOrderType',
        'mom-mom-ECenterIssueClass',
        'mom-ECenterIndicatorodule',
        'mom-JudgmentRules',
        'mom-ECenterIssueLevel',
        'mom-ECenterHandleLevel'
    ]
    let formatUrl = `/api/system/dict/type/query?types=${dictNames.toString()}&languageCode=zh_CN`

    try {
        const result = await request(formatUrl, {
            method: "GET"
        });
        return result;
    } catch (error) {
        throw error;
    }
}

/**
 * 获取工厂列表
*/

async function getFactoryNameList() {
    try {
        const result = await request("/api/mom-system/sysOrg/getOrgByOrgLevelWithAuth?orgLevel=011", {
            method: "GET"
        });
        return result;
    } catch (error) {
        throw error;
    }
}

/**
 * 获取车间，线体列表
*/

async function getWorkshopAndLineList(code) {
    try {
        const result = await request(`/api/mom-data/momFactory/getChildrenByCode?factoryModelCode=${code}`, {
            method: "GET"
        });
        return result;
    } catch (error) {
        throw error;
    }
}

/**
 * 获取班组
*/

async function getTeamList(code) {
    try {
        const result = await request(`/api/mom-event/momEventInfo/getSop?code=${code}`, {
            method: "GET"
        });
        return result;
    } catch (error) {
        throw error;
    }
}

/**
 * 组织人员搜索
*/

async function searchUser(payload) {
    try {
        const result = await request(`/api/mom-system/sysUser/queryByUserAndOrg?name=${payload}&typeCode=LDAP&queryType=user`, {
            method: "GET"
        });
        return result;
    } catch (error) {
        throw error;
    }
}

/**
 * OQC抽检不良率
*/

async function queryOqcSpotCheck(params) {
    try {
        // 解构参数，保留原始参数对象
        const { date, teamName, ...otherParams } = params;

        // 构建请求体，将date映射为targetTime，并合并其他参数
        const requestBody = {
            targetTime: date,
            teamName: teamName,
            ...otherParams
        };

        // 发送请求
        const result = await request(`/api/mom-edw-report/oqcSpotCheck/queryOqcSpotCheck`, {
            method: "POST",
            body: JSON.stringify(requestBody)
        });
        return result;
    } catch (error) {
        throw error;
    }
}

/**
 * 表格工单数据
*/

async function queryWorkOrderData(params) {
    try {
        const result = await request("/api/mom-event/momEventInfo/getEventListPage", {
            method: "POST",
            body: JSON.stringify(params)
        });
        return result;
    } catch (error) {

    }
}

/**
 * 表格工单数据 - 升级
*/

async function queryWorkOrderDataUpgrade(eventId) {
    try {
        const result = await request(`/api/mom-event/momEventInfo/upgrade?eventId=${eventId}`, {
            method: "GET",
        });
        return result;
    } catch (error) {

    }
}

/**
 * 获取日生产计划执行率-机型
*/

async function getDailyProductPlanRate(params) {
    try {
        // 解构参数，保留原始参数对象
        const { date,orgParams } = params;

        // 构建请求体，将date映射为targetTime，并合并其他参数
        const requestBody = {
            targetTime: date,
            ...orgParams
        };

        // 发送请求
        const result = await request(`/api/mom-edw-report/dms/getDailyProductPlanRate`, {
            method: "POST",
            body: JSON.stringify(requestBody)
        });
        return result;
    } catch (error) {
        throw error;
    }
}
/**
 * 直通率
*/

async function getPassThroughRate(params) {
    try {
        // 解构参数，保留原始参数对象
        const { date, orgParams } = params;

        // 构建请求体，将date映射为targetTime，并合并其他参数
        const requestBody = {
            targetTime: date,
            ...orgParams
        };

        // 发送请求
        const result = await request(`/api/mom-edw-report/dms/getPassThroughRate`, {
            method: "POST",
            body: JSON.stringify(requestBody)
        });
        return result;
    } catch (error) {
        throw error;
    }
}


/**
 * 焊接泄露率
*/

async function getWeldingLeak(params) {
    try {
        // 解构参数，保留原始参数对象
        const { date, orgParams } = params;

        // 构建请求体，将date映射为targetTime，并合并其他参数
        const requestBody = {
            targetTime: date,
            ...orgParams
        };

        // 发送请求
        const result = await request(`/api/mom-edw-report/dms/getWeldingLeak`, {
            method: "POST",
            body: JSON.stringify(requestBody)
        });
        return result;
    } catch (error) {
        throw error;
    }
}

/**
 * 停产损失时间
*/

async function getDowntimeLoss(params) {
    try {
        // 解构参数，保留原始参数对象
        const { date, orgParams } = params;

        // 构建请求体，将date映射为targetTime，并合并其他参数
        const requestBody = {
            targetTime: date,
            ...orgParams
        };

        // 发送请求
        const result = await request(`/api/mom-edw-report/dms/getDowntimeLoss`, {
            method: "POST",
            body: JSON.stringify(requestBody)
        });
        return result;
    } catch (error) {
        console.error("获取停产损失时间失败:", error);
        throw error;
    }
}

/**
 * 过站损失时间
*/

async function getStationLoss(params) {
    try {
        // 解构参数，保留原始参数对象
        const { date, orgParams } = params;

        // 构建请求体，将date映射为targetTime，并合并其他参数
        const requestBody = {
            targetTime: date,
            ...orgParams
        };

        // 发送请求
        const result = await request(`/api/mom-edw-report/dms/getStationLoss`, {
            method: "POST",
            body: JSON.stringify(requestBody)
        });
        return result;
    } catch (error) {
        console.error("获取过站损失时间失败:", error);
        throw error;
    }
}

/**
 * 手创工单提交
*/

async function saveHandMade(params) {
    try {
        const result = await request("/api/mom-event/momEventInfo/saveHandmade", {
            method: "POST",
            body: JSON.stringify(params)
        });
        return result;
    } catch (error) {
        console.log("手创工单失败", error)
    }
}

/**
 * 获取QRCode
*/

async function getQrCode(lineCode){
    try {
        const result = await request(`/mom/mom/momMeetingRecord/getMeetingQrCode?user_key=vylsr6pgfrvyv8wpqk07woflfqwpatx5&meetingTypeCode=%E8%BD%A6%E9%97%B4%E7%AB%99%E7%AB%8B%E4%BC%9A%E8%AE%AE-%E4%B8%A4%E5%B0%8F%E6%97%B6&momFactoryModelCode=${lineCode}`, {
            method: "GET",
        });
        return result;
    } catch (error) {
        console.log("获取QRCode失败", error)
        throw error;
    }
}

export {
    getAllFactory,
    getAllDictsData,
    getFactoryNameList,
    getWorkshopAndLineList,
    searchUser,
    queryOqcSpotCheck,
    queryWorkOrderData,
    getDailyProductPlanRate,
    getPassThroughRate,
    getWeldingLeak,
    getDowntimeLoss,
    getStationLoss,
    saveHandMade,
    queryWorkOrderDataUpgrade,
    getTeamList,
    getQrCode
}