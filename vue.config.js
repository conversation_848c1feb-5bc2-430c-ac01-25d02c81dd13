/*
 * @Description: vue-cli配置文件
 * @Author: g<PERSON><PERSON><PERSON>
 * @Date: 2020-08-28 15:37:33
 * @LastEditors: yueshengqi.ex
 * @LastEditTime: 2025-05-15 16:17:01
 * @FilePath: /vue-com/vue.config.js
 */
const path = require("path");
function resolve(dir) {
  return path.join(__dirname, dir);
}

module.exports = {
  parallel: false,
  lintOnSave: false,
  configureWebpack: {
    externals: {
      vue: "Vue2",
      axios: "axios2",
      "vue-router": "VueRouter2",
      vuex: "Vuex2",
      moment: "moment2",
      "ant-design-vue": "antd2",
      vuedraggable: "vuedraggable2",
    },
  },
  chainWebpack: (config) => {
    config.resolve.alias.set("@", resolve("src"));
    config.module
      .rule("images")
      .use("url-loader")
      .loader("url-loader")
      .tap((options) => Object.assign(options, { limit: 300000 }));
  },
  css: {
    extract: false, // css提取独立文件
    loaderOptions: {
      less: {
        modifyVars: {
          "primary-color": "#00AAA6",
          "font-size-base": "14px",
          "font-family": "Microsoft YaHei",
        },
        javascriptEnabled: true,
      },
    },
  },
  devServer: {
    port: 8082,
    proxy: {
      "/api": {
        target: "http://gateway-mom-test.clouddev.hisense.com", // 测试
        // target: "http://pangea-gateway-mom.prd-gyl-hisense-mom:9527", // 正式
        // target:"https://mom.hisense.com",
        pathRewrite: { "^/api": "" },
        changeOrigin: true,
      },
      "/higpt/": {
        target: "https://inner-apisix.hisense.com",
        pathRewrite: { "^/higpt/": "" },
        changeOrigin: true,
      },
      "/mom/": {
        target: "https://inner-apisix-test.hisense.com",
        pathRewrite: { "^/mom/": "" },
        changeOrigin: true,
      }
    },
  },
};
