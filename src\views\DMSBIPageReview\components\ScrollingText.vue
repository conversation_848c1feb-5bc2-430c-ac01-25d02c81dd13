<template>
  <div class="scrolling-text-container" v-if="showScroll">
    <div class="scrolling-text" ref="scrollTextRef" :style="scrollStyle">
      <span v-html="formattedText"></span>
      <span class="spacer"></span>
      <span v-html="formattedText"></span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScrollingText',
  props: {
    // 要滚动的文本内容
    text: {
      type: String,
      required: true
    },
    // 滚动速度，值越小速度越快
    speed: {
      type: Number,
      default: 30
    },
    // 是否显示滚动文本
    showScroll: {
      type: Boolean,
      default: true
    },
    // 暗色主题
    darkTheme: {
      type: Boolean,
      default: false
    },
    // 是否为质量或工时损失板块
    isQualityOrLostTime: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      scrollStyle: {
        animationDuration: '30s'
      },
      textWidth: 0,
      containerWidth: 0
    };
  },
  computed: {
    formattedText() {
      if (!this.text) return '';

      // 处理交付板块的达成率颜色
      let formattedText = this.text.replace(/达成率(\d+(\.\d+)?)%/g, (match, rate) => {
        const rateNum = parseFloat(rate);
        let color = rateNum >= 100 ? '#0256FF' : '#EA0000';
        return `达成率<span style="color: ${color};">${rate}%</span>`;
      });

      // 处理质量和工时损失的数字为红色
      if (this.isQualityOrLostTime) {
        // 匹配数字并添加红色样式
        formattedText = formattedText.replace(/(\d+(\.\d+)?)(台|次|分钟|%)/g, (match, num, decimal, unit) => {
          return `<span style="color: #EA0000;">${num}</span>${unit}`;
        });
      }

      return formattedText;
    }
  },
  watch: {
    text() {
      // 文本内容变化时重新计算
      this.$nextTick(() => {
        this.calculateScrollSpeed();
      });
    },
    showScroll(newVal) {
      if (newVal) {
        // 显示时重新计算
        this.$nextTick(() => {
          this.calculateScrollSpeed();
        });
      }
    }
  },
  mounted() {
    // 组件挂载后计算滚动速度
    this.$nextTick(() => {
      this.calculateScrollSpeed();
    });

    // 监听窗口大小变化，重新计算滚动速度
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    handleResize() {
      // 窗口大小变化时重新计算
      this.calculateScrollSpeed();
    },
    calculateScrollSpeed() {
      if (!this.$refs.scrollTextRef || !this.showScroll) return;

      // 获取文本元素和容器的宽度
      const textElement = this.$refs.scrollTextRef.firstChild;
      const containerElement = this.$refs.scrollTextRef.parentNode;

      if (!textElement || !containerElement) return;

      // 计算文本实际宽度
      this.textWidth = textElement.offsetWidth;
      this.containerWidth = containerElement.offsetWidth;

      // 如果文本宽度小于容器宽度，不需要滚动
      if (this.textWidth <= this.containerWidth) {
        this.scrollStyle = {
          animationDuration: '0s',
          transform: 'translateX(0)',
          animationName: 'none'
        };
        return;
      }

      // 根据文本长度动态计算滚动时间
      // 文本越长，滚动时间越长
      const duration = Math.max(this.textWidth / this.speed, 5);

      this.scrollStyle = {
        animationDuration: `${duration}s`
      };
    }
  }
};
</script>

<style scoped>
.scrolling-text-container {
  width: 100%;
  overflow: hidden;
  padding: 4px 0;
  position: relative;
}

.scrolling-text {
  white-space: nowrap;
  display: inline-block;
  animation: scrollText linear infinite;
}

.scrolling-text span {
  display: inline-block;
  color: var(--text-color, #333);
  font-size: 14px;
}

.scrolling-text .spacer {
  display: inline-block;
  width: 50px;
}

@keyframes scrollText {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* 暗色主题样式 */
.dark-theme .scrolling-text span {
  color: #fff;
}
</style>
